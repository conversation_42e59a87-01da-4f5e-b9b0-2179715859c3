# Kyutai 1B STT Transcription MVP

Real-time speech-to-text transcription using Kyutai's 1B model.

## Prerequisites

- Python 3.8+
- Moshi installed in venv (with the 1B STT model)
- Working microphone
- CUDA GPU (optional, for faster inference)

## Quick Start

### Test with an audio file:
```bash
source .venv/bin/activate
python test_transcribe.py your_audio.wav
```

### Real-time transcription (requires microphone):
```bash
source .venv/bin/activate
python simple_transcribe.py
```

**Note for WSL users**: Audio passthrough may not work. Use the file-based test instead.

## Alternative Methods

### 1. Direct Model Loading (Advanced)
```bash
python stt_1b_transcribe.py
```
This loads the model directly but requires more setup.

### 2. Client-Server Mode
For the full Moshi experience with dialog capabilities:

Terminal 1:
```bash
python -m moshi.server --hf-repo kyutai/stt-1b-en_fr
```

Terminal 2:
```bash
python moshi_transcribe.py
```

## Model Info

- Model: `kyutai/stt-1b-en_fr`
- Languages: English and French
- Latency: ~0.5 seconds
- Features: Semantic voice activity detection

## Notes

- First run will download the 1B model (~4GB)
- Uses 24kHz audio sampling
- Press Ctrl+C to stop