#!/usr/bin/env python3
"""
Minimal Moshi STT transcription MVP
Shows text as you talk in real-time
"""

import asyncio
import argparse
import sys
import queue
import aiohttp
import numpy as np
import sounddevice as sd
import sphn


class SimpleTranscriber:
    def __init__(self, websocket_url="ws://localhost:8998/api/chat"):
        self.websocket_url = websocket_url
        self.sample_rate = 24000
        self.channels = 1
        self.frame_size = 1920
        self._done = False
        self._opus_writer = sphn.OpusStreamWriter(self.sample_rate)
        
        # Audio input stream
        self._in_stream = sd.InputStream(
            samplerate=self.sample_rate,
            channels=self.channels,
            blocksize=self.frame_size,
            callback=self._on_audio_input,
        )
        
    def _on_audio_input(self, in_data, frames, time_, status):
        """Callback for audio input - encodes to Opus"""
        if not self._done:
            self._opus_writer.append_pcm(in_data[:, 0])
    
    async def _send_audio_loop(self, websocket):
        """Continuously send audio data to server"""
        while not self._done:
            await asyncio.sleep(0.001)
            msg = self._opus_writer.read_bytes()
            if len(msg) > 0:
                try:
                    # Send audio message (kind=1)
                    await websocket.send_bytes(b"\x01" + msg)
                except Exception as e:
                    print(f"\nError sending audio: {e}")
                    self._done = True
                    return
    
    async def _receive_loop(self, websocket):
        """Receive and display transcribed text"""
        try:
            async for message in websocket:
                if message.type == aiohttp.WSMsgType.CLOSED:
                    print("\nConnection closed")
                    break
                elif message.type == aiohttp.WSMsgType.ERROR:
                    print(f"\nError: {websocket.exception()}")
                    break
                elif message.type == aiohttp.WSMsgType.BINARY:
                    data = message.data
                    if isinstance(data, bytes) and len(data) > 0:
                        kind = data[0]
                        if kind == 2:  # Text message
                            text = data[1:].decode('utf-8', errors='ignore')
                            # Print without newline for continuous text
                            print(text, end='', flush=True)
        except Exception as e:
            print(f"\nError receiving: {e}")
            self._done = True
    
    async def run(self):
        """Main run loop"""
        print(f"Connecting to {self.websocket_url}...")
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.ws_connect(self.websocket_url) as websocket:
                    print("Connected! Start speaking...\n")
                    
                    with self._in_stream:
                        await asyncio.gather(
                            self._send_audio_loop(websocket),
                            self._receive_loop(websocket)
                        )
            except aiohttp.ClientError as e:
                print(f"Connection error: {e}")
                print("\nMake sure the Moshi server is running:")
                print("  python -m moshi.server")
    
    def stop(self):
        """Stop transcription"""
        self._done = True


async def main():
    parser = argparse.ArgumentParser(description="Moshi STT Transcription")
    parser.add_argument("--host", default="localhost", help="Server hostname")
    parser.add_argument("--port", default=8998, type=int, help="Server port")
    parser.add_argument("--url", help="Full WebSocket URL (overrides host/port)")
    
    args = parser.parse_args()
    
    # Build WebSocket URL
    if args.url:
        ws_url = args.url
    else:
        ws_url = f"ws://{args.host}:{args.port}/api/chat"
    
    transcriber = SimpleTranscriber(ws_url)
    
    try:
        await transcriber.run()
    except KeyboardInterrupt:
        print("\n\nStopping transcription...")
        transcriber.stop()


if __name__ == "__main__":
    print("Moshi STT Transcription MVP")
    print("-" * 30)
    asyncio.run(main())