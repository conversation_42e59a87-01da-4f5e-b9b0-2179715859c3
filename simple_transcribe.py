#!/usr/bin/env python3
"""
Simple real-time transcription using <PERSON><PERSON>'s run_inference
Records audio in chunks and processes them
"""

import subprocess
import sounddevice as sd
import soundfile as sf
import numpy as np
import tempfile
import os
import threading
import queue
import time


class SimpleTranscriber:
    def __init__(self, chunk_duration=2.0):
        self.sample_rate = 24000
        self.channels = 1
        self.chunk_duration = chunk_duration  # Process audio in chunks
        self.chunk_samples = int(self.sample_rate * chunk_duration)
        
        self.audio_queue = queue.Queue()
        self.recording = True
        
    def record_audio(self):
        """Record audio continuously"""
        print("Recording started. Speak now...")
        
        with sd.InputStream(samplerate=self.sample_rate, 
                          channels=self.channels,
                          dtype='float32') as stream:
            while self.recording:
                audio_chunk, _ = stream.read(self.chunk_samples)
                self.audio_queue.put(audio_chunk)
    
    def process_audio(self):
        """Process audio chunks through Moshi"""
        while self.recording or not self.audio_queue.empty():
            try:
                audio_chunk = self.audio_queue.get(timeout=0.5)
                
                # Save chunk to temporary file
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                    tmp_path = tmp_file.name
                    sf.write(tmp_path, audio_chunk, self.sample_rate)
                
                # Run Moshi inference
                try:
                    result = subprocess.run([
                        'python', '-m', 'moshi.run_inference',
                        '--hf-repo', 'kyutai/stt-1b-en_fr',
                        tmp_path
                    ], capture_output=True, text=True)
                    
                    if result.stdout:
                        # Extract transcribed text from output
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line and not line.startswith('[') and not line.startswith('INFO'):
                                print(line, end=' ', flush=True)
                    
                except Exception as e:
                    print(f"\nError running inference: {e}")
                
                finally:
                    # Clean up temp file
                    os.unlink(tmp_path)
                    
            except queue.Empty:
                continue
    
    def run(self):
        """Run transcription"""
        # Start recording thread
        record_thread = threading.Thread(target=self.record_audio)
        record_thread.start()
        
        # Process audio in main thread
        try:
            self.process_audio()
        except KeyboardInterrupt:
            print("\n\nStopping...")
            self.recording = False
            record_thread.join()


def main():
    print("Simple Moshi 1B STT Transcription")
    print("=" * 35)
    
    # Check for audio devices
    try:
        devices = sd.query_devices()
        print(f"Available audio devices: {len(devices)}")
        if len(devices) == 0:
            print("\nNo audio devices found!")
            print("If you're using WSL, audio passthrough may not be configured.")
            return
    except Exception as e:
        print(f"Error checking audio devices: {e}")
        print("\nIf you're using WSL, you may need to:")
        print("1. Use WSL2 with WSLg for audio support")
        print("2. Or run this on native Windows/Linux")
        return
    
    print("This will process audio in 2-second chunks")
    print("Press Ctrl+C to stop\n")
    
    transcriber = SimpleTranscriber(chunk_duration=2.0)
    transcriber.run()


if __name__ == "__main__":
    main()