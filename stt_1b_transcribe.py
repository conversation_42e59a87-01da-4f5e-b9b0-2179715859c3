#!/usr/bin/env python3
"""
Real-time STT transcription using Kyutai's 1B model
Shows text as you talk with ~0.5s delay
"""

import asyncio
import argparse
import sys
import torch
import sounddevice as sd
import numpy as np
from collections import deque
import time

from moshi.models import loaders
from moshi.run_inference import InferenceState


class RealTimeTranscriber:
    def __init__(self, model_name="kyutai/stt-1b-en_fr", device="cuda"):
        self.model_name = model_name
        self.device = device if torch.cuda.is_available() else "cpu"
        self.sample_rate = 24000
        self.channels = 1
        self.frame_size = 1920  # 80ms at 24kHz
        self.buffer_size = 12000  # 0.5s buffer for the model's delay
        
        print(f"Loading model {model_name} on {self.device}...")
        
        # Load the model
        checkpoint_info = loaders.load_checkpoint(
            model_name, 
            device=self.device,
            model_type="stt"
        )
        
        self.mimi = checkpoint_info.mimi
        self.lm = checkpoint_info.lm
        self.text_tokenizer = checkpoint_info.text_tokenizer
        
        # Initialize inference state
        self.inference_state = InferenceState(
            checkpoint_info=checkpoint_info,
            mimi=self.mimi,
            text_tokenizer=self.text_tokenizer,
            lm=self.lm,
            batch_size=1,
            cfg_coef=1.0,
            device=self.device,
            use_sampling=False,
            temp=1.0,
            temp_text=0.0  # Deterministic text generation
        )
        
        # Audio buffer
        self.audio_buffer = deque(maxlen=int(self.sample_rate * 2))  # 2 second rolling buffer
        self.processing = False
        self._done = False
        
        # Audio input stream
        self._in_stream = sd.InputStream(
            samplerate=self.sample_rate,
            channels=self.channels,
            blocksize=self.frame_size,
            callback=self._on_audio_input,
        )
        
        print("Model loaded successfully!")
        
    def _on_audio_input(self, in_data, frames, time_, status):
        """Callback for audio input"""
        if not self._done:
            # Add audio to buffer
            audio_data = in_data[:, 0].copy()
            self.audio_buffer.extend(audio_data)
    
    async def process_audio_loop(self):
        """Process buffered audio through the model"""
        while not self._done:
            await asyncio.sleep(0.1)  # Process every 100ms
            
            if len(self.audio_buffer) >= self.buffer_size and not self.processing:
                self.processing = True
                
                # Get audio chunk to process
                audio_chunk = np.array(list(self.audio_buffer))[:self.buffer_size]
                
                # Convert to tensor and add batch dimension
                audio_tensor = torch.from_numpy(audio_chunk).float()
                audio_tensor = audio_tensor.unsqueeze(0).unsqueeze(0)  # [batch, channels, samples]
                audio_tensor = audio_tensor.to(self.device)
                
                # Run inference
                try:
                    results = self.inference_state.run(audio_tensor)
                    
                    # Extract text tokens from results
                    for text_tokens, audio_tokens in results:
                        if text_tokens:
                            # Decode text tokens
                            text_ids = [t.item() for t in text_tokens if t.item() != self.text_tokenizer.eos_id()]
                            if text_ids:
                                text = self.text_tokenizer.decode(text_ids)
                                print(text, end='', flush=True)
                
                except Exception as e:
                    print(f"\nError processing audio: {e}")
                
                self.processing = False
    
    async def run(self):
        """Main run loop"""
        print("\nStarting transcription. Speak clearly...\n")
        
        with self._in_stream:
            try:
                await self.process_audio_loop()
            except KeyboardInterrupt:
                pass
    
    def stop(self):
        """Stop transcription"""
        self._done = True


async def main():
    parser = argparse.ArgumentParser(description="Real-time STT using Kyutai 1B model")
    parser.add_argument("--model", default="kyutai/stt-1b-en_fr", help="Model name/path")
    parser.add_argument("--device", default="cuda", choices=["cuda", "cpu"], help="Device to use")
    
    args = parser.parse_args()
    
    transcriber = RealTimeTranscriber(model_name=args.model, device=args.device)
    
    try:
        await transcriber.run()
    except KeyboardInterrupt:
        print("\n\nStopping transcription...")
        transcriber.stop()


if __name__ == "__main__":
    print("Kyutai 1B STT Real-time Transcription")
    print("=" * 40)
    asyncio.run(main())