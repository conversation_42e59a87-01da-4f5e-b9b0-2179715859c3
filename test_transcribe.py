#!/usr/bin/env python3
"""
Test Moshi 1B STT with an audio file
"""

import subprocess
import sys
import os


def transcribe_file(audio_path):
    """Transcribe an audio file using Moshi 1B"""
    print(f"Transcribing: {audio_path}")
    print("-" * 40)
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'moshi.run_inference',
            '--hf-repo', 'kyutai/stt-1b-en_fr',
            audio_path
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error: {result.stderr}")
            return
        
        # Extract transcribed text from output
        print("\nTranscription:")
        lines = result.stdout.strip().split('\n')
        for line in lines:
            # Skip log/info lines
            if line and not line.startswith('[') and not line.startswith('INFO') and not line.startswith('WARNING'):
                print(line)
                
    except Exception as e:
        print(f"Error running inference: {e}")


def main():
    if len(sys.argv) < 2:
        print("Usage: python test_transcribe.py <audio_file>")
        print("\nExample:")
        print("  python test_transcribe.py speech.wav")
        print("  python test_transcribe.py recording.mp3")
        sys.exit(1)
    
    audio_file = sys.argv[1]
    
    if not os.path.exists(audio_file):
        print(f"Error: File '{audio_file}' not found")
        sys.exit(1)
    
    print("Moshi 1B STT Test")
    print("=" * 40)
    print("Note: First run will download the model (~4GB)\n")
    
    transcribe_file(audio_file)


if __name__ == "__main__":
    main()